"use client";

import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import * as Dialog from "@radix-ui/react-dialog";
import { useTranslations } from "next-intl";
import { Product, Game, UserInfo } from "../lib/games/types";
import { getCurrencySymbol } from "../lib/games/utils";
import { confirmPayment, fetchPaymentConfigs, PaymentConfig, PaymentMethod } from "../lib/games/api";

interface PaymentSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product | null;
  game: Game;
  userInfo: UserInfo | null;
  locale: string;
  selectedServerId: string;
  selectedRoleId: string;
  siteId: number;
}

export default function PaymentSelectionModal({
  isOpen,
  onClose,
  product,
  game,
  userInfo,
  locale,
  selectedServerId,
  selectedRoleId,
  siteId
}: PaymentSelectionModalProps) {
  const [paymentConfigs, setPaymentConfigs] = useState<PaymentConfig[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<"pending" | "processing" | "success" | "error">("pending");
  const scrollYRef = useRef(0);
  const t = useTranslations();
  
  // 阻止背景滚动
  useEffect(() => {
    const handleScroll = (e: TouchEvent) => {
      if (isOpen) {
        e.preventDefault();
      }
    };
    
    if (isOpen) {
      scrollYRef.current = window.scrollY;
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollYRef.current}px`;
      document.body.style.width = '100%';
      document.body.style.overflowY = 'hidden';
      document.addEventListener('touchmove', handleScroll, { passive: false });
    } else {
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      document.body.style.overflowY = '';
      window.scrollTo(0, scrollYRef.current);
      document.removeEventListener('touchmove', handleScroll);
    }

    return () => {
      document.removeEventListener('touchmove', handleScroll);
    };
  }, [isOpen]);

  // 获取支付配置和重置状态
  useEffect(() => {
    if (isOpen) {
      // 重置状态
      setPaymentStatus("pending");
      setError(null);
      setSelectedPaymentMethod(null);

      // 加载支付配置
      loadPaymentConfigs();
    }
  }, [isOpen]);

  const loadPaymentConfigs = async () => {
    try {
      setLoading(true);
      const configs = await fetchPaymentConfigs();

      // 对配置和支付方式进行排序
      const sortedConfigs = configs
        .sort((a, b) => a.sort - b.sort)
        .map(config => ({
          ...config,
          methods: config.methods.sort((a, b) => a.sort - b.sort)
        }));

      setPaymentConfigs(sortedConfigs);

      // 自动选择第一个支付方式
      if (sortedConfigs.length > 0 && sortedConfigs[0].methods.length > 0) {
        setSelectedPaymentMethod(sortedConfigs[0].methods[0]);
      }
    } catch (err) {
      console.error('获取支付配置失败:', err);
      setError(t('errors.fetchPaymentConfigsFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async () => {
    if (!product || !userInfo || !selectedPaymentMethod) {
      setError(t('payment.pleaseSelectMethod'));
      return;
    }

    if (!selectedServerId || !selectedRoleId) {
      setError(t('payment.pleaseSelectServerRole'));
      return;
    }

    if (!userInfo.productCode) {
      setError(t('payment.missingProductCode'));
      return;
    }

    setPaymentStatus("processing");
    setError(null);

    try {
      // 构建确认支付参数
      const confirmPaymentParams = {
        siteId: siteId, // 使用传入的 siteId
        configId: selectedPaymentMethod.configId,
        methodId: selectedPaymentMethod.methodId,
        amount: product.price,
        currency: product.currency || game.currency || 'KRW', // 使用商品或游戏的货币类型
        productName: product.title || product.id,
        skuCode: product.skuCode || product.id,
        userId: userInfo.uid.toString(),
        roleId: selectedRoleId,
        serverId: selectedServerId,
        roleName: userInfo.roleName || '',
        returnUrl: window.location.origin + '/payment/return' // 支付返回地址
      };

      console.log('发起确认支付请求，参数:', confirmPaymentParams);

      const response = await confirmPayment(confirmPaymentParams);

      console.log('确认支付接口响应:', response);

      if (response.code === 0 && response.data.paymentUrl) {
        console.log('获取到支付链接:', response.data.paymentUrl);

        // 计算屏幕中心位置
        const width = 800;
        const height = 600;
        const left = (window.screen.width - width) / 2;
        const top = (window.screen.height - height) / 2;

        // 在新窗口中打开支付链接
        const paymentWindow = window.open(
          response.data.paymentUrl,
          '_blank',
          `width=${width},height=${height},left=${left},top=${top},toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes`
        );

        if (!paymentWindow) {
          setError(t('payment.allowPopup'));
        } else {
          setPaymentStatus("success");
          // 延迟关闭弹窗
          setTimeout(() => {
            onClose();
          }, 1500);
        }
      } else {
        throw new Error(response.msg || t('errors.paymentFailed'));
      }
    } catch (err) {
      console.error('支付失败:', err);
      setPaymentStatus("error");
      setError(err instanceof Error ? err.message : t('errors.paymentFailed'));
    }
  };

  const getCurrencySymbolSafe = () => {
    if (!product) return '';
    return getCurrencySymbol(product.currency || game.currency);
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Portal>
        <Dialog.Overlay 
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40" 
          style={{ touchAction: 'none' }}
          onClick={(e) => e.stopPropagation()}
        />
        <Dialog.Content
          className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl max-h-[85vh] overflow-auto rounded-2xl bg-gradient-to-br from-zinc-900 to-zinc-950 border border-zinc-800 shadow-2xl z-50 p-6 custom-scrollbar"
          style={{ touchAction: 'pan-y' }}
          onClick={(e) => e.stopPropagation()}
        >
          {paymentStatus === "processing" && (
            <div className="flex flex-col items-center py-8">
              <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mb-4"></div>
              <p className="text-white font-medium">{t('payment.processing')}</p>
            </div>
          )}

          {paymentStatus === "success" && (
            <div className="flex flex-col items-center py-8">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10 text-green-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <p className="text-white font-medium">{t('payment.success')}</p>
              <p className="text-zinc-400 text-sm mt-2">{t('payment.successDesc')}</p>
            </div>
          )}

          {paymentStatus === "pending" && (
            <div>
              <div className="flex justify-between items-start mb-6">
                <Dialog.Title className="text-xl font-bold text-white">
                  {t('payment.selectMethod')}
                </Dialog.Title>
                <Dialog.Close asChild>
                  <button
                    className="rounded-full p-1 hover:bg-zinc-800 transition-colors"
                    aria-label="关闭"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-zinc-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </Dialog.Close>
              </div>

              {error && (
                <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
                  <p className="text-sm text-red-400">{error}</p>
                </div>
              )}

              {/* 商品信息 */}
              {product && (
                <div className="mb-6 p-4 bg-zinc-800/50 border border-zinc-700/30 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-zinc-700">
                      <Image
                        src={product.imageUrl || "/images/games/1.png"}
                        alt={product.title || product.id}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-white font-medium">{product.title || product.id}</h3>
                      <p className="text-zinc-400 text-sm">{game.title}</p>
                      <p className="text-xl font-bold text-indigo-400 mt-1">
                        {getCurrencySymbolSafe()}{product.price}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* 支付方式选择 */}
              {loading ? (
                <div className="flex justify-center py-8">
                  <div className="w-8 h-8 border-2 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
                </div>
              ) : (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-zinc-400 mb-4">{t('payment.selectMethod')}</h3>
                  <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-60 overflow-y-auto custom-scrollbar">
                    {paymentConfigs.map((config) => (
                      config.methods.map((method) => (
                        <button
                          key={`${config.id}-${method.id}`}
                          onClick={() => setSelectedPaymentMethod(method)}
                          className={`relative p-3 rounded-lg border transition-all flex flex-col items-center space-y-2 min-h-[80px] ${
                            selectedPaymentMethod?.id === method.id
                              ? "bg-indigo-500/20 border-indigo-500/50 text-indigo-400"
                              : "bg-zinc-800/50 border-zinc-700/30 text-zinc-300 hover:bg-zinc-800"
                          }`}
                        >
                          <div className="relative w-8 h-8 rounded overflow-hidden bg-white flex-shrink-0">
                            <Image
                              src={method.iconUrl}
                              alt={method.methodName}
                              fill
                              className="object-contain"
                            />
                          </div>
                          <div className="text-center">
                            <p className="font-medium text-xs leading-tight">{method.methodName}</p>
                            <p className="text-xs text-zinc-500 mt-1">{config.name}</p>
                          </div>
                          {selectedPaymentMethod?.id === method.id && (
                            <div className="absolute top-2 right-2">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-4 w-4 text-indigo-400"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M5 13l4 4L19 7"
                                />
                              </svg>
                            </div>
                          )}
                        </button>
                      ))
                    ))}
                  </div>
                </div>
              )}

              {/* 支付按钮 */}
              <div className="flex gap-4">
                <button
                  onClick={onClose}
                  className="flex-1 py-3 px-4 bg-zinc-800 text-zinc-400 font-medium rounded-lg hover:bg-zinc-700 transition-colors"
                >
                  {t('payment.cancel')}
                </button>
                <button
                  onClick={handlePayment}
                  disabled={!selectedPaymentMethod || loading}
                  className={`flex-1 py-3 px-4 font-medium rounded-lg transition-colors ${
                    selectedPaymentMethod && !loading
                      ? "bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white"
                      : "bg-zinc-700 text-zinc-500 cursor-not-allowed"
                  }`}
                >
                  {product ? `${t('payment.pay')} ${getCurrencySymbolSafe()}${product.price}` : t('payment.confirm')}
                </button>
              </div>
            </div>
          )}

          {paymentStatus === "error" && (
            <div className="flex flex-col items-center py-8">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10 text-red-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <p className="text-white font-medium mb-2">{t('payment.failed')}</p>
              {error && <p className="text-red-400 text-sm text-center">{error}</p>}
              <button
                onClick={() => setPaymentStatus("pending")}
                className="mt-4 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
              >
                {t('payment.retry')}
              </button>
            </div>
          )}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
