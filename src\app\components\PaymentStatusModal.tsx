"use client";

import { useEffect } from "react";
import * as Dialog from "@radix-ui/react-dialog";
import { useTranslations } from "next-intl";

interface PaymentStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  status: 'success' | 'failed' | 'cancelled';
  message?: string;
  orderNo?: string;
}

export default function PaymentStatusModal({
  isOpen,
  onClose,
  status,
  message,
  orderNo
}: PaymentStatusModalProps) {
  const t = useTranslations();

  // 自动关闭弹窗（成功状态3秒后关闭，失败状态不自动关闭）
  useEffect(() => {
    if (isOpen && status === 'success') {
      const timer = setTimeout(() => {
        onClose();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isOpen, status, onClose]);

  const getStatusConfig = () => {
    switch (status) {
      case 'success':
        return {
          // 豪华渐变背景 - 深邃的黑色到翠绿渐变
          bgGradient: 'from-slate-950 via-slate-900 to-emerald-950/40',
          // 多层边框效果
          borderGradient: 'from-emerald-400/50 via-green-500/30 to-emerald-600/50',
          // 强烈的发光效果
          glowColor: 'shadow-2xl shadow-emerald-500/40',
          // 图标容器渐变
          iconBg: 'from-emerald-400/20 via-green-500/30 to-emerald-600/20',
          iconGlow: 'shadow-lg shadow-emerald-400/50',
          // 颜色主题
          primaryColor: 'text-emerald-400',
          secondaryColor: 'text-emerald-300',
          accentColor: 'text-white',
          // 文本内容
          title: t('payment.paymentSuccess'),
          subtitle: t('payment.paymentSuccessSubtitle'),
          // 豪华图标设计
          icon: (
            <div className="relative">
              <svg className="h-12 w-12 sm:h-14 sm:w-14 lg:h-16 lg:w-16 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {/* 图标背景光环 */}
              <div className="absolute inset-0 bg-emerald-400/20 rounded-full blur-xl animate-pulse" />
              <div className="absolute inset-2 bg-emerald-500/10 rounded-full blur-lg animate-pulse" style={{ animationDelay: '0.5s' }} />
            </div>
          ),
          // 动画效果
          particles: true,
          buttonGradient: 'from-emerald-500 via-green-600 to-emerald-700',
          buttonHover: 'from-emerald-400 via-green-500 to-emerald-600',
          buttonShadow: 'shadow-lg shadow-emerald-500/30'
        };
      case 'failed':
        return {
          bgGradient: 'from-slate-950 via-slate-900 to-red-950/40',
          borderGradient: 'from-red-400/50 via-rose-500/30 to-red-600/50',
          glowColor: 'shadow-2xl shadow-red-500/40',
          iconBg: 'from-red-400/20 via-rose-500/30 to-red-600/20',
          iconGlow: 'shadow-lg shadow-red-400/50',
          primaryColor: 'text-red-400',
          secondaryColor: 'text-red-300',
          accentColor: 'text-white',
          title: t('payment.paymentFailed'),
          subtitle: t('payment.paymentFailedSubtitle'),
          icon: (
            <div className="relative">
              <svg className="h-12 w-12 sm:h-14 sm:w-14 lg:h-16 lg:w-16 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
              </svg>
              <div className="absolute inset-0 bg-red-400/20 rounded-full blur-xl animate-pulse" />
              <div className="absolute inset-2 bg-red-500/10 rounded-full blur-lg animate-pulse" style={{ animationDelay: '0.5s' }} />
            </div>
          ),
          particles: false,
          buttonGradient: 'from-red-500 via-rose-600 to-red-700',
          buttonHover: 'from-red-400 via-rose-500 to-red-600',
          buttonShadow: 'shadow-lg shadow-red-500/30'
        };
      case 'cancelled':
        return {
          bgGradient: 'from-slate-950 via-slate-900 to-amber-950/40',
          borderGradient: 'from-amber-400/50 via-yellow-500/30 to-orange-600/50',
          glowColor: 'shadow-2xl shadow-amber-500/40',
          iconBg: 'from-amber-400/20 via-yellow-500/30 to-orange-600/20',
          iconGlow: 'shadow-lg shadow-amber-400/50',
          primaryColor: 'text-amber-400',
          secondaryColor: 'text-amber-300',
          accentColor: 'text-white',
          title: t('payment.paymentCancelled'),
          subtitle: t('payment.paymentCancelledSubtitle'),
          icon: (
            <div className="relative">
              <svg className="h-12 w-12 sm:h-14 sm:w-14 lg:h-16 lg:w-16 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
              <div className="absolute inset-0 bg-amber-400/20 rounded-full blur-xl animate-pulse" />
              <div className="absolute inset-2 bg-amber-500/10 rounded-full blur-lg animate-pulse" style={{ animationDelay: '0.5s' }} />
            </div>
          ),
          particles: false,
          buttonGradient: 'from-amber-500 via-yellow-600 to-orange-700',
          buttonHover: 'from-amber-400 via-yellow-500 to-orange-600',
          buttonShadow: 'shadow-lg shadow-amber-500/30'
        };
      default:
        return {
          bgGradient: 'from-slate-950 to-slate-900',
          borderGradient: 'from-slate-600/50 to-slate-700/50',
          glowColor: 'shadow-xl shadow-slate-500/20',
          iconBg: 'from-slate-500/20 to-slate-600/20',
          iconGlow: 'shadow-md shadow-slate-400/30',
          primaryColor: 'text-slate-400',
          secondaryColor: 'text-slate-500',
          accentColor: 'text-white',
          title: '处理中',
          subtitle: '请稍候...',
          icon: null,
          particles: false,
          buttonGradient: 'from-slate-600 to-slate-700',
          buttonHover: 'from-slate-500 to-slate-600',
          buttonShadow: 'shadow-md shadow-slate-500/20'
        };
    }
  };

  const config = getStatusConfig();

  // 如果弹窗未打开，不渲染
  if (!isOpen) {
    return null;
  }

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Portal>
        {/* 豪华背景遮罩 */}
        <Dialog.Overlay className="fixed inset-0 bg-black/90 backdrop-blur-md z-50 animate-in fade-in duration-300" />

        <Dialog.Content className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-sm sm:max-w-md lg:max-w-xl z-50 mx-4 sm:mx-6 animate-in zoom-in-95 fade-in duration-300">
          {/* 主容器 - 多层设计 */}
          <div className="relative">
            {/* 外层光环 */}
            <div className={`absolute -inset-4 bg-gradient-to-r ${config.borderGradient} rounded-[2rem] blur-xl opacity-60 animate-pulse`} />

            {/* 中层边框 */}
            <div className={`absolute -inset-1 bg-gradient-to-r ${config.borderGradient} rounded-[1.75rem] opacity-80`} />

            {/* 主体容器 */}
            <div className={`relative rounded-3xl bg-gradient-to-br ${config.bgGradient} ${config.glowColor} overflow-hidden border border-white/10`}>

              {/* 顶部装饰条 */}
              <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${config.borderGradient}`} />

              {/* 背景纹理层 */}
              <div className="absolute inset-0 opacity-30">
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent" />
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]" />
                {/* 网格纹理 */}
                <div className="absolute inset-0 opacity-20" style={{
                  backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0)`,
                  backgroundSize: '20px 20px'
                }} />
              </div>

              {/* 豪华粒子效果 - 移动端优化 */}
              {config.particles && status === 'success' && (
                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                  {/* 大粒子 - 移动端显示较少 */}
                  {[...Array(8)].map((_, i) => (
                    <div
                      key={`large-${i}`}
                      className={`absolute w-2 sm:w-3 h-2 sm:h-3 bg-emerald-400/40 rounded-full animate-bounce blur-sm ${i >= 4 ? 'hidden sm:block' : ''}`}
                      style={{
                        left: `${15 + i * 12}%`,
                        top: `${20 + (i % 3) * 25}%`,
                        animationDelay: `${i * 0.3}s`,
                        animationDuration: '3s'
                      }}
                    />
                  ))}
                  {/* 小粒子 - 移动端显示较少 */}
                  {[...Array(12)].map((_, i) => (
                    <div
                      key={`small-${i}`}
                      className={`absolute w-1 sm:w-1.5 h-1 sm:h-1.5 bg-emerald-300/60 rounded-full animate-ping ${i >= 6 ? 'hidden sm:block' : ''}`}
                      style={{
                        left: `${10 + i * 8}%`,
                        top: `${15 + (i % 4) * 20}%`,
                        animationDelay: `${i * 0.2}s`,
                        animationDuration: '2s'
                      }}
                    />
                  ))}
                  {/* 光线效果 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-emerald-400/10 to-transparent animate-pulse" />
                </div>
              )}

              {/* 豪华关闭按钮 - 响应式 */}
              <Dialog.Close asChild>
                <button
                  className="absolute top-4 right-4 sm:top-6 sm:right-6 lg:top-8 lg:right-8 group z-20 p-2 sm:p-3 rounded-full bg-white/5 hover:bg-white/10 backdrop-blur-xl border border-white/20 hover:border-white/30 transition-all duration-300 hover:scale-110"
                  aria-label="关闭"
                >
                  <div className="relative">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 sm:h-6 sm:w-6 text-white/70 group-hover:text-white transition-colors duration-300"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      strokeWidth={2}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    {/* 按钮光环 */}
                    <div className="absolute inset-0 bg-white/10 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                </button>
              </Dialog.Close>

              {/* 主要内容区域 - 响应式布局 */}
              <div className="relative px-6 py-8 sm:px-8 sm:py-12 lg:px-12 lg:py-16 text-center">
                {/* 豪华状态图标区域 - 响应式 */}
                <div className="relative mx-auto mb-8 sm:mb-10 lg:mb-12">
                  {/* 外层装饰环 - 移动端简化 */}
                  <div className={`hidden sm:block absolute -inset-6 lg:-inset-8 rounded-full bg-gradient-to-r ${config.borderGradient} opacity-20 animate-spin`} style={{ animationDuration: '8s' }} />
                  <div className={`hidden sm:block absolute -inset-4 lg:-inset-6 rounded-full bg-gradient-to-r ${config.borderGradient} opacity-30 animate-spin`} style={{ animationDuration: '6s', animationDirection: 'reverse' }} />

                  {/* 主图标容器 */}
                  <div className="relative">
                    {/* 背景光环 */}
                    <div className={`absolute -inset-3 sm:-inset-4 rounded-full bg-gradient-to-br ${config.iconBg} ${config.iconGlow} animate-pulse`} />
                    <div className={`absolute -inset-1 sm:-inset-2 rounded-full bg-gradient-to-br ${config.iconBg} opacity-60 animate-pulse`} style={{ animationDelay: '0.5s' }} />

                    {/* 图标主体 - 响应式尺寸 */}
                    <div className={`relative w-24 h-24 sm:w-28 sm:h-28 lg:w-32 lg:h-32 rounded-full bg-gradient-to-br ${config.iconBg} border-2 border-white/20 flex items-center justify-center ${config.primaryColor} backdrop-blur-xl`}>
                      {/* 内部装饰 */}
                      <div className="absolute inset-1.5 sm:inset-2 rounded-full border border-white/10" />
                      <div className="absolute inset-3 sm:inset-4 rounded-full bg-gradient-to-br from-white/10 to-transparent" />

                      {/* 图标内容 */}
                      <div className="relative z-10">
                        {config.icon}
                      </div>

                      {/* 旋转装饰 */}
                      <div className={`absolute inset-0 rounded-full border-2 border-transparent border-t-white/30 animate-spin`} style={{ animationDuration: '3s' }} />
                    </div>
                  </div>

                  {/* 底部阴影 */}
                  <div className={`absolute top-full left-1/2 -translate-x-1/2 w-16 sm:w-20 lg:w-24 h-4 sm:h-5 lg:h-6 bg-gradient-to-r ${config.borderGradient} opacity-20 blur-xl rounded-full`} />
                </div>

                {/* 豪华标题区域 - 响应式 */}
                <div className="mb-6 sm:mb-8 lg:mb-10">
                  <Dialog.Title className={`text-3xl sm:text-4xl lg:text-5xl font-black mb-3 sm:mb-4 ${config.accentColor} tracking-tight`}>
                    <span className="bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent">
                      {config.title}
                    </span>
                  </Dialog.Title>
                  <div className={`text-lg sm:text-xl font-semibold ${config.secondaryColor} mb-2`}>
                    {config.subtitle}
                  </div>
                  {/* 装饰线 */}
                  <div className={`mx-auto w-16 sm:w-20 lg:w-24 h-1 bg-gradient-to-r ${config.borderGradient} rounded-full`} />
                </div>

                {/* 豪华消息卡片 - 响应式 */}
                {message && (
                  <div className="mb-6 sm:mb-8 relative">
                    {/* 卡片光环 */}
                    <div className={`absolute -inset-1 bg-gradient-to-r ${config.borderGradient} rounded-2xl opacity-30 blur-sm`} />

                    {/* 卡片主体 */}
                    <div className="relative p-4 sm:p-5 lg:p-6 bg-white/5 border border-white/10 rounded-2xl backdrop-blur-xl">
                      <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-2xl" />
                      <p className="relative text-white/95 text-base sm:text-lg leading-relaxed font-medium">
                        {message}
                      </p>
                    </div>
                  </div>
                )}

                {/* 豪华订单信息 - 响应式 */}
                {orderNo && (
                  <div className="mb-6 sm:mb-8 relative">
                    <div className={`absolute -inset-1 bg-gradient-to-r ${config.borderGradient} rounded-2xl opacity-20 blur-sm`} />
                    <div className="relative p-4 sm:p-5 bg-black/20 border border-white/10 rounded-2xl backdrop-blur-xl">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                        <span className="text-white/70 text-sm sm:text-base font-semibold">{t('payment.orderNo')}</span>
                        <div className="flex items-center gap-2 sm:gap-3">
                          <span className="text-white font-mono text-sm sm:text-base bg-white/10 px-3 sm:px-4 py-1.5 sm:py-2 rounded-xl border border-white/20 break-all">
                            {orderNo}
                          </span>
                          {/* 复制按钮 */}
                          <button
                            onClick={() => navigator.clipboard?.writeText(orderNo)}
                            className="p-1.5 sm:p-2 rounded-lg bg-white/5 hover:bg-white/10 border border-white/20 hover:border-white/30 transition-all duration-200 flex-shrink-0"
                          >
                            <svg className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-white/70" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 豪华按钮区域 - 响应式 */}
                <div className="space-y-4 sm:space-y-6">
                  {/* 主要操作按钮 */}
                  <div className="relative group">
                    {/* 按钮光环 */}
                    <div className={`absolute -inset-1 bg-gradient-to-r ${config.buttonGradient} rounded-2xl opacity-60 group-hover:opacity-80 blur-sm transition-all duration-300`} />

                    {/* 按钮主体 */}
                    <button
                      onClick={onClose}
                      className={`relative w-full py-4 sm:py-5 px-6 sm:px-8 bg-gradient-to-r ${config.buttonGradient} hover:${config.buttonHover} rounded-2xl font-bold text-white text-base sm:text-lg transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] ${config.buttonShadow} border border-white/20`}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-transparent to-white/20 rounded-2xl" />
                      <span className="relative flex items-center justify-center gap-2 sm:gap-3">
                        {status === 'success' && (
                          <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                          </svg>
                        )}
                        {status === 'failed' && (
                          <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                        )}
                        {status === 'cancelled' && (
                          <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        )}
                        <span className="truncate">
                          {status === 'success' ? t('ui.continue') : status === 'failed' ? '重试支付' : t('ui.close')}
                        </span>
                      </span>
                    </button>
                  </div>

                  {/* 自动关闭提示 */}
                  {status === 'success' && (
                    <div className="flex items-center justify-center gap-2 sm:gap-3 text-white/60 text-sm sm:text-base">
                      <div className="relative">
                        <svg className="h-4 w-4 sm:h-5 sm:w-5 animate-spin" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="3" />
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                        </svg>
                        <div className="absolute inset-0 bg-emerald-400/20 rounded-full blur-md animate-pulse" />
                      </div>
                      <span className="font-medium">3秒后自动关闭</span>
                    </div>
                  )}

                  {/* 底部装饰 */}
                  <div className="flex justify-center">
                    <div className={`w-12 sm:w-16 h-1 bg-gradient-to-r ${config.borderGradient} rounded-full opacity-50`} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
