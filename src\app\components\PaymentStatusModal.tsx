"use client";

import { useEffect } from "react";
import * as Dialog from "@radix-ui/react-dialog";
import { useTranslations } from "next-intl";

interface PaymentStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  status: 'success' | 'failed' | 'cancelled';
  message?: string;
  orderNo?: string;
}

export default function PaymentStatusModal({
  isOpen,
  onClose,
  status,
  message,
  orderNo
}: PaymentStatusModalProps) {
  const t = useTranslations();

  // 自动关闭弹窗（成功状态3秒后关闭，失败状态不自动关闭）
  useEffect(() => {
    if (isOpen && status === 'success') {
      const timer = setTimeout(() => {
        onClose();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isOpen, status, onClose]);

  const getStatusConfig = () => {
    switch (status) {
      case 'success':
        return {
          bgColor: 'from-green-600 to-emerald-600',
          iconBg: 'bg-green-500/20',
          iconColor: 'text-green-400',
          title: t('payment.paymentSuccess'),
          icon: (
            <svg className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )
        };
      case 'failed':
        return {
          bgColor: 'from-red-600 to-rose-600',
          iconBg: 'bg-red-500/20',
          iconColor: 'text-red-400',
          title: t('payment.paymentFailed'),
          icon: (
            <svg className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          )
        };
      case 'cancelled':
        return {
          bgColor: 'from-yellow-600 to-orange-600',
          iconBg: 'bg-yellow-500/20',
          iconColor: 'text-yellow-400',
          title: t('payment.paymentCancelled'),
          icon: (
            <svg className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          )
        };
      default:
        return {
          bgColor: 'from-gray-600 to-gray-700',
          iconBg: 'bg-gray-500/20',
          iconColor: 'text-gray-400',
          title: '',
          icon: null
        };
    }
  };

  const config = getStatusConfig();

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 transition-opacity duration-200" />
        <Dialog.Content className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-md z-50 transition-all duration-200 scale-100 opacity-100">
          <div className={`relative rounded-2xl bg-gradient-to-br ${config.bgColor} p-8 shadow-2xl border border-white/10`}>
            {/* 关闭按钮 */}
            <Dialog.Close asChild>
              <button
                className="absolute top-4 right-4 p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
                aria-label="关闭"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </Dialog.Close>

            {/* 内容区域 */}
            <div className="text-center">
              {/* 图标 */}
              <div className={`mx-auto w-24 h-24 ${config.iconBg} rounded-full flex items-center justify-center mb-6 ${config.iconColor}`}>
                {config.icon}
              </div>

              {/* 标题 */}
              <Dialog.Title className="text-3xl font-bold text-white mb-4">
                {config.title}
              </Dialog.Title>

              {/* 消息 */}
              {message && (
                <p className="text-white/90 text-lg mb-4 leading-relaxed">
                  {message}
                </p>
              )}

              {/* 订单号 */}
              {orderNo && (
                <div className="bg-white/10 rounded-lg p-3 mb-6">
                  <p className="text-white/70 text-sm mb-1">{t('payment.orderNo')}</p>
                  <p className="text-white font-mono text-lg">{orderNo}</p>
                </div>
              )}

              {/* 按钮区域 */}
              <div className="space-y-3">
                <button
                  onClick={onClose}
                  className="w-full py-3 px-6 bg-white/20 hover:bg-white/30 text-white font-medium rounded-lg transition-colors backdrop-blur-sm"
                >
                  {status === 'success' ? t('ui.continue') : t('ui.close')}
                </button>
                
                {status === 'success' && (
                  <p className="text-white/60 text-sm">
                    3秒后自动关闭
                  </p>
                )}
              </div>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
