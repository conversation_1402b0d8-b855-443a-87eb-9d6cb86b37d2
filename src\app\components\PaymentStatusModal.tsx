"use client";

import { useEffect } from "react";
import * as Dialog from "@radix-ui/react-dialog";
import { useTranslations } from "next-intl";

interface PaymentStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  status: 'success' | 'failed' | 'cancelled';
  message?: string;
  orderNo?: string;
}

export default function PaymentStatusModal({
  isOpen,
  onClose,
  status,
  message,
  orderNo
}: PaymentStatusModalProps) {
  const t = useTranslations();

  // 自动关闭弹窗（成功状态3秒后关闭，失败状态不自动关闭）
  useEffect(() => {
    if (isOpen && status === 'success') {
      const timer = setTimeout(() => {
        onClose();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isOpen, status, onClose]);

  const getStatusConfig = () => {
    switch (status) {
      case 'success':
        return {
          // 简洁现代的配色方案
          bgColor: 'bg-white',
          borderColor: 'border-green-100',
          iconBg: 'bg-green-50',
          iconColor: 'text-green-600',
          titleColor: 'text-gray-900',
          messageColor: 'text-gray-600',
          buttonBg: 'bg-green-600 hover:bg-green-700',
          buttonText: 'text-white',
          // 内容
          title: t('payment.paymentSuccess'),
          icon: (
            <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
            </svg>
          )
        };
      case 'failed':
        return {
          bgColor: 'bg-white',
          borderColor: 'border-red-100',
          iconBg: 'bg-red-50',
          iconColor: 'text-red-600',
          titleColor: 'text-gray-900',
          messageColor: 'text-gray-600',
          buttonBg: 'bg-red-600 hover:bg-red-700',
          buttonText: 'text-white',
          title: t('payment.paymentFailed'),
          icon: (
            <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          )
        };
      case 'cancelled':
        return {
          bgColor: 'bg-white',
          borderColor: 'border-amber-100',
          iconBg: 'bg-amber-50',
          iconColor: 'text-amber-600',
          titleColor: 'text-gray-900',
          messageColor: 'text-gray-600',
          buttonBg: 'bg-amber-600 hover:bg-amber-700',
          buttonText: 'text-white',
          title: t('payment.paymentCancelled'),
          icon: (
            <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          )
        };
      default:
        return {
          bgColor: 'bg-white',
          borderColor: 'border-gray-200',
          iconBg: 'bg-gray-50',
          iconColor: 'text-gray-600',
          titleColor: 'text-gray-900',
          messageColor: 'text-gray-600',
          buttonBg: 'bg-gray-600 hover:bg-gray-700',
          buttonText: 'text-white',
          title: '处理中',
          icon: (
            <svg className="w-8 h-8 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth={4} />
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
          )
        };
    }
  };

  const config = getStatusConfig();

  // 如果弹窗未打开，不渲染
  if (!isOpen) {
    return null;
  }

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Portal>
        {/* 简洁背景遮罩 */}
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 animate-in fade-in duration-200" />

        <Dialog.Content className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-sm sm:max-w-md z-50 mx-4 animate-in zoom-in-95 fade-in duration-200">
          {/* 主容器 - 简洁设计 */}
          <div className={`relative ${config.bgColor} rounded-2xl shadow-xl border ${config.borderColor} overflow-hidden`}>

            {/* 简洁关闭按钮 */}
            <Dialog.Close asChild>
              <button
                className="absolute top-4 right-4 p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                aria-label="关闭"
              >
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </Dialog.Close>

            {/* 主要内容区域 */}
            <div className="px-6 py-8 sm:px-8 sm:py-10 text-center">
              {/* 简洁状态图标 */}
              <div className="flex justify-center mb-6">
                <div className={`w-16 h-16 sm:w-20 sm:h-20 rounded-full ${config.iconBg} flex items-center justify-center ${config.iconColor}`}>
                  {config.icon}
                </div>
              </div>

              {/* 简洁标题 */}
              <Dialog.Title className={`text-xl sm:text-2xl font-semibold ${config.titleColor} mb-4`}>
                {config.title}
              </Dialog.Title>

              {/* 消息内容 */}
              {message && (
                <p className={`${config.messageColor} text-sm sm:text-base leading-relaxed mb-6`}>
                  {message}
                </p>
              )}

              {/* 订单信息 */}
              {orderNo && (
                <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                    <span className="text-gray-600 text-sm font-medium">{t('payment.orderNo')}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-gray-900 font-mono text-sm bg-white px-3 py-1.5 rounded border break-all">
                        {orderNo}
                      </span>
                      <button
                        onClick={() => navigator.clipboard?.writeText(orderNo)}
                        className="p-1.5 rounded text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                        title="复制订单号"
                      >
                        <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* 简洁按钮区域 */}
              <div className="space-y-4">
                <button
                  onClick={onClose}
                  className={`w-full py-3 px-6 rounded-lg font-medium transition-colors duration-200 ${config.buttonBg} ${config.buttonText}`}
                >
                  {status === 'success' ? t('ui.continue') : status === 'failed' ? '重试支付' : t('ui.close')}
                </button>

                {/* 自动关闭提示 */}
                {status === 'success' && (
                  <p className="text-gray-500 text-sm text-center">
                    3秒后自动关闭
                  </p>
                )}
              </div>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
