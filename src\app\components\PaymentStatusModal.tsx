"use client";

import { useEffect } from "react";
import * as Dialog from "@radix-ui/react-dialog";
import { useTranslations } from "next-intl";

interface PaymentStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  status: 'success' | 'failed' | 'cancelled';
  message?: string;
  orderNo?: string;
}

export default function PaymentStatusModal({
  isOpen,
  onClose,
  status,
  message,
  orderNo
}: PaymentStatusModalProps) {
  const t = useTranslations();

  // 自动关闭弹窗（成功状态3秒后关闭，失败状态不自动关闭）
  useEffect(() => {
    if (isOpen && status === 'success') {
      const timer = setTimeout(() => {
        onClose();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isOpen, status, onClose]);

  const getStatusConfig = () => {
    switch (status) {
      case 'success':
        return {
          bgGradient: 'from-zinc-900 via-zinc-900 to-green-900/30',
          borderColor: 'border-green-500/30',
          glowColor: 'shadow-green-500/20',
          iconBg: 'from-green-500/20 to-emerald-500/20',
          iconColor: 'text-green-400',
          accentColor: 'text-green-400',
          title: t('payment.paymentSuccess'),
          subtitle: '交易已成功完成',
          icon: (
            <svg className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          particles: true
        };
      case 'failed':
        return {
          bgGradient: 'from-zinc-900 via-zinc-900 to-red-900/30',
          borderColor: 'border-red-500/30',
          glowColor: 'shadow-red-500/20',
          iconBg: 'from-red-500/20 to-rose-500/20',
          iconColor: 'text-red-400',
          accentColor: 'text-red-400',
          title: t('payment.paymentFailed'),
          subtitle: '支付过程中出现问题',
          icon: (
            <svg className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          ),
          particles: false
        };
      case 'cancelled':
        return {
          bgGradient: 'from-zinc-900 via-zinc-900 to-yellow-900/30',
          borderColor: 'border-yellow-500/30',
          glowColor: 'shadow-yellow-500/20',
          iconBg: 'from-yellow-500/20 to-orange-500/20',
          iconColor: 'text-yellow-400',
          accentColor: 'text-yellow-400',
          title: t('payment.paymentCancelled'),
          subtitle: '支付操作已取消',
          icon: (
            <svg className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          ),
          particles: false
        };
      default:
        return {
          bgGradient: 'from-zinc-900 to-zinc-950',
          borderColor: 'border-zinc-700',
          glowColor: 'shadow-zinc-500/10',
          iconBg: 'from-zinc-500/20 to-zinc-600/20',
          iconColor: 'text-zinc-400',
          accentColor: 'text-zinc-400',
          title: '处理中',
          subtitle: '请稍候...',
          icon: null,
          particles: false
        };
    }
  };

  const config = getStatusConfig();

  // 如果弹窗未打开，不渲染
  if (!isOpen) {
    return null;
  }

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-lg z-50 mx-4">
          <div className={`relative rounded-3xl bg-gradient-to-br ${config.bgGradient} border ${config.borderColor} shadow-2xl ${config.glowColor} overflow-hidden`}>
            {/* 背景装饰 */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent pointer-events-none" />

            {/* 成功状态的粒子效果 */}
            {config.particles && status === 'success' && (
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-2 h-2 bg-green-400/60 rounded-full animate-bounce"
                    style={{
                      left: `${20 + i * 15}%`,
                      top: `${30 + (i % 2) * 20}%`,
                      animationDelay: `${i * 0.2}s`,
                      animationDuration: '2s'
                    }}
                  />
                ))}
              </div>
            )}

            {/* 关闭按钮 */}
            <Dialog.Close asChild>
              <button
                className="absolute top-6 right-6 p-2 rounded-full bg-zinc-800/50 hover:bg-zinc-700/70 transition-all duration-200 backdrop-blur-sm border border-zinc-700/50 z-10"
                aria-label="关闭"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-zinc-300 hover:text-white transition-colors"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth={2}
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </Dialog.Close>

            {/* 主要内容区域 */}
            <div className="relative p-8 pt-12 text-center">
              {/* 状态图标 */}
              <div className="relative mx-auto mb-8">
                <div className={`w-28 h-28 rounded-full bg-gradient-to-br ${config.iconBg} border border-white/10 flex items-center justify-center ${config.iconColor} shadow-lg`}>
                  {config.icon}
                </div>
                {/* 图标光环效果 */}
                <div className={`absolute inset-0 w-28 h-28 rounded-full bg-gradient-to-br ${config.iconBg} opacity-30 animate-pulse`} />
              </div>

              {/* 标题和副标题 */}
              <div className="mb-6">
                <Dialog.Title className={`text-3xl font-bold mb-2 ${config.accentColor}`}>
                  {config.title}
                </Dialog.Title>
                <p className="text-zinc-400 text-lg font-medium">
                  {config.subtitle}
                </p>
              </div>

              {/* 消息内容 */}
              {message && (
                <div className="mb-6 p-4 bg-zinc-800/30 border border-zinc-700/30 rounded-xl backdrop-blur-sm">
                  <p className="text-white/90 text-base leading-relaxed">
                    {message}
                  </p>
                </div>
              )}

              {/* 订单信息 */}
              {orderNo && (
                <div className="mb-6 p-4 bg-zinc-800/50 border border-zinc-700/50 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-zinc-400 text-sm font-medium">{t('payment.orderNo')}</span>
                    <span className="text-white font-mono text-sm bg-zinc-700/50 px-3 py-1 rounded-lg">
                      {orderNo}
                    </span>
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="space-y-4">
                <button
                  onClick={onClose}
                  className={`w-full py-4 px-6 rounded-xl font-semibold text-white transition-all duration-200 ${
                    status === 'success'
                      ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 shadow-lg shadow-green-500/25'
                      : status === 'failed'
                      ? 'bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 shadow-lg shadow-red-500/25'
                      : 'bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700 shadow-lg shadow-yellow-500/25'
                  }`}
                >
                  {status === 'success' ? t('ui.continue') : status === 'failed' ? '重试支付' : t('ui.close')}
                </button>

                {status === 'success' && (
                  <p className="text-zinc-500 text-sm flex items-center justify-center gap-2">
                    <svg className="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                    </svg>
                    3秒后自动关闭
                  </p>
                )}
              </div>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
