"use client";

import { useState, useEffect, use, useMemo, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { useTranslations, useLocale } from "next-intl";
import { useSearchParams, useRouter } from "next/navigation";
import Layout from "../../../components/Layout";
import ProductCard from "../../../components/ProductCard";

import LoginModal from "../../../components/LoginModal";
import ServerRoleSelector from "../../../components/ServerRoleSelector";
import PaymentSelectionModal from "../../../components/PaymentSelectionModal";
import PaymentStatusModal from "../../../components/PaymentStatusModal";
import { fetchGameData, fetchSiteInfo, getUserSiteInfo } from "../../../lib/games/api";
import { extractGameData, getCurrencySymbol } from "../../../lib/games/utils";
import { ApiGame, ServerRoleInfo, UserInfo } from "../../../lib/games/types";


interface GamePageProps {
  params: Promise<{
    gameId: string;
    locale: string;
  }>;
}



export default function GamePage(props: GamePageProps) {
  // 使用React.use解包Promise
  const params = use(props.params);
  const gameId = params.gameId;
  const t = useTranslations();
  const locale = useLocale() as string;
  const searchParams = useSearchParams();
  const router = useRouter();
  
  // 状态管理
  const [apiGameData, setApiGameData] = useState<ApiGame | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [productId, setProductId] = useState<string | null>(null);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [serverUrl, setServerUrl] = useState<string>('');
  const [serverRoleList, setServerRoleList] = useState<ServerRoleInfo[]>([]);
  const [selectedServerId, setSelectedServerId] = useState<string>('');
  const [selectedRoleId, setSelectedRoleId] = useState<string>('');
  const [siteId, setSiteId] = useState<number | null>(null);
  const isLoadingRef = useRef(false);

  // 支付状态弹窗相关状态
  const [paymentStatusModal, setPaymentStatusModal] = useState<{
    isOpen: boolean;
    status: 'success' | 'failed' | 'cancelled';
    message: string;
    orderNo: string;
  }>({ isOpen: false, status: 'success', message: '', orderNo: '' });

  // 清除URL中的支付状态参数
  const clearPaymentStatusFromUrl = () => {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.delete('paymentStatus');
    currentUrl.searchParams.delete('paymentMessage');
    currentUrl.searchParams.delete('orderNo');

    // 使用 router.replace 来更新URL，不会在浏览器历史中添加新条目
    router.replace(currentUrl.pathname + currentUrl.search);
  };

  // 处理支付状态弹窗关闭
  const handlePaymentStatusModalClose = () => {
    setPaymentStatusModal(prev => ({ ...prev, isOpen: false }));
    // 清除URL参数，防止刷新页面时再次显示弹窗
    clearPaymentStatusFromUrl();
  };

  // 检测支付状态
  useEffect(() => {
    const rawPaymentStatus = searchParams.get('paymentStatus');
    const paymentMessage = searchParams.get('paymentMessage') || '';
    const orderNo = searchParams.get('orderNo') || '';

    if (rawPaymentStatus) {
      const upperStatus = rawPaymentStatus.toUpperCase();

      // 如果是 PENDING 状态，不显示弹窗，直接清除URL参数
      if (upperStatus === 'PENDING' || upperStatus === 'PROCESSING') {
        console.log('支付状态为 PENDING，不显示弹窗');
        clearPaymentStatusFromUrl();
        return;
      }

      // 处理大小写转换和状态映射
      let normalizedStatus: 'success' | 'failed' | 'cancelled';

      if (upperStatus === 'SUCCESS' || upperStatus === 'COMPLETED') {
        normalizedStatus = 'success';
      } else if (upperStatus === 'FAILED' || upperStatus === 'FAILURE' || upperStatus === 'ERROR') {
        normalizedStatus = 'failed';
      } else if (upperStatus === 'CANCELLED' || upperStatus === 'CANCELED') {
        normalizedStatus = 'cancelled';
      } else {
        // 默认处理未知状态
        normalizedStatus = 'failed';
      }

      // 处理空消息的情况
      let displayMessage = paymentMessage;
      if (!displayMessage || displayMessage.trim() === '') {
        switch (normalizedStatus) {
          case 'success':
            displayMessage = t('payment.paymentSuccessMessage');
            break;
          case 'failed':
            displayMessage = t('payment.paymentFailedMessage');
            break;
          case 'cancelled':
            displayMessage = t('payment.paymentCancelledMessage');
            break;
        }
      }

      setPaymentStatusModal({
        isOpen: true,
        status: normalizedStatus,
        message: decodeURIComponent(displayMessage),
        orderNo: orderNo
      });

      console.log('支付状态弹窗数据:', {
        rawStatus: rawPaymentStatus,
        normalizedStatus,
        message: displayMessage,
        orderNo
      });
    }
  }, [searchParams]);

  // 从本地存储获取用户信息和区服角色信息
  useEffect(() => {
    const loadUserInfoAndServerRoles = async () => {
      // 首先尝试获取当前游戏特定的用户信息
      let gameSpecificUser = localStorage.getItem(`gameUser_${gameId}`);
      let parsedUser = null;

      if (gameSpecificUser) {
        try {
          parsedUser = JSON.parse(gameSpecificUser);
          console.log('找到当前游戏的登录信息:', parsedUser);
        } catch (e) {
          console.error('解析游戏特定用户信息失败:', e);
          localStorage.removeItem(`gameUser_${gameId}`);
        }
      } else {
        // 如果没有当前游戏的特定信息，检查旧的 gameUser 格式（向后兼容）
        const legacyUser = localStorage.getItem('gameUser');
        if (legacyUser && !gameSpecificUser) {
          try {
            const legacyUserData = JSON.parse(legacyUser);
            if (legacyUserData.gameId === gameId) {
              parsedUser = legacyUserData;
              console.log('使用兼容的用户信息:', parsedUser);
            }
          } catch (e) {
            console.error('解析兼容用户信息失败:', e);
            localStorage.removeItem('gameUser');
          }
        }
      }

      if (parsedUser) {
        setUserInfo(parsedUser);

        // 设置已选择的区服和角色
        if (parsedUser.serverId && parsedUser.roleId) {
          setSelectedServerId(parsedUser.serverId);
          setSelectedRoleId(parsedUser.roleId);
        }

        // 如果用户已登录且有必要的信息，重新获取区服角色信息
        if (parsedUser.uid && parsedUser.username && parsedUser.productCode && parsedUser.callbackKey && serverUrl) {
          try {
            console.log('用户已登录，重新获取区服角色信息...');
            const siteInfo = await getUserSiteInfo(serverUrl, {
              uid: parsedUser.uid,
              username: parsedUser.username,
              productCode: parsedUser.productCode,
              callbackKey: parsedUser.callbackKey
            });

            // 更新区服角色信息
            setServerRoleList(siteInfo);
            localStorage.setItem('serverRoleList', JSON.stringify(siteInfo));
            console.log('成功更新区服角色信息:', siteInfo);
          } catch (error) {
            console.error('重新获取区服角色信息失败:', error);
            // 如果获取失败，尝试从本地存储加载
            const storedServerRoleList = localStorage.getItem('serverRoleList');
            if (storedServerRoleList) {
              try {
                const parsedList = JSON.parse(storedServerRoleList);
                setServerRoleList(parsedList);
                console.log('使用本地存储的区服角色信息');
              } catch (e) {
                console.error('解析区服角色信息失败:', e);
                localStorage.removeItem('serverRoleList');
              }
            }
          }
        } else {
          // 如果用户信息不完整，只从本地存储加载区服角色信息
          const storedServerRoleList = localStorage.getItem('serverRoleList');
          if (storedServerRoleList) {
            try {
              const parsedList = JSON.parse(storedServerRoleList);
              setServerRoleList(parsedList);
            } catch (e) {
              console.error('解析区服角色信息失败:', e);
              localStorage.removeItem('serverRoleList');
            }
          }
        }
      } else {
        // 如果没有用户信息，清理区服角色信息
        setServerRoleList([]);
      }
    };

    loadUserInfoAndServerRoles();
  }, [gameId, serverUrl]); // 添加serverUrl作为依赖
  
  // 从API获取游戏数据
  useEffect(() => {
    async function loadGameData() {
      if (!gameId) {
        setError(t('errors.gameIdNotFound'));
        setLoading(false);
        return;
      }

      // 防止重复请求
      if (isLoadingRef.current) {
        console.log('正在加载中，跳过重复请求');
        return;
      }

      console.log('开始加载游戏数据，gameId:', gameId); // 调试日志
      isLoadingRef.current = true;
      setLoading(true);
      try {
        // 获取站点信息
        const siteInfo = await fetchSiteInfo(gameId);
        console.log('站点信息:', siteInfo); // 调试日志

        // 获取游戏数据
        const gameData = await fetchGameData(gameId);

        if (siteInfo && 'productCode' in siteInfo && gameData) {
          console.log('获取到的 productCode:', siteInfo.productCode); // 调试日志

          // 将 productCode 添加到游戏数据中
          const enrichedGameData = {
            ...gameData,
            productCode: siteInfo.productCode
          };

          setApiGameData(enrichedGameData);
          console.log('更新后的游戏数据:', enrichedGameData); // 调试日志

          // 设置 siteId
          if (typeof siteInfo.id === 'number') {
            setSiteId(siteInfo.id);
            console.log('设置 siteId:', siteInfo.id); // 调试日志
          }

          // 设置 productId
          if (typeof siteInfo.productId === 'string') {
            setProductId(siteInfo.productId);
          } else if (typeof siteInfo.id === 'number') {
            setProductId(String(siteInfo.id));
          }

          // 设置 serverUrl（如果站点信息中有的话）
          if ('serverUrl' in siteInfo && typeof siteInfo.serverUrl === 'string') {
            setServerUrl(siteInfo.serverUrl);
          }
        } else {
          console.error('站点信息中没有 productCode:', siteInfo); // 调试日志
          throw new Error('无法获取商品编码');
        }
      } catch (err) {
        console.error('加载游戏数据失败:', err); // 详细的错误日志
        setError(t('errors.loadGameDataFailed'));
      } finally {
        setLoading(false);
        isLoadingRef.current = false;
      }
    }

    loadGameData();

    // 清理函数
    return () => {
      isLoadingRef.current = false;
    };
  }, [gameId]);
  
  // 根据当前语言提取游戏数据，使用useMemo避免无限重新计算
  const game = useMemo(() => {
    if (!apiGameData) return null;
    return extractGameData(apiGameData, locale as any);
  }, [apiGameData, locale]);

  // 确保游戏有货币信息（如果API没有返回货币类型，使用默认值）
  const gameWithCurrency = useMemo(() => {
    return game ? {
      ...game,
      currency: game.currency || 'KRW'  // 如果没有货币信息，默认使用韩元
    } : null;
  }, [game]);
  
  // 选中的产品，使用useMemo优化
  const selectedProduct = useMemo(() => {
    return selectedProductId && gameWithCurrency
      ? gameWithCurrency.products.find((product) => product.id === selectedProductId)
      : null;
  }, [selectedProductId, gameWithCurrency]);
  
  const handleProductSelect = (productId: string) => {
    setSelectedProductId(productId);
  };

  // 处理区服角色选择
  const handleServerRoleSelect = (serverId: string, roleId: string, roleName: string) => {
    setSelectedServerId(serverId);
    setSelectedRoleId(roleId);

    // 更新用户信息并保存到localStorage
    if (userInfo) {
      const updatedUserInfo = {
        ...userInfo,
        serverId: serverId,
        roleId: roleId,
        roleName: roleName
      };
      setUserInfo(updatedUserInfo);

      // 保存到游戏特定的存储
      localStorage.setItem(`gameUser_${gameId}`, JSON.stringify(updatedUserInfo));

      // 为了向后兼容，也保存到原来的 gameUser 键
      localStorage.setItem('gameUser', JSON.stringify(updatedUserInfo));
    }
  };
  
  const handlePaymentClick = () => {
    // 检查是否有选中的产品和游戏数据
    if (!selectedProductId || !gameWithCurrency) {
      return;
    }

    // 如果未登录，显示登录弹窗
    if (!userInfo) {
      setIsLoginModalOpen(true);
      return;
    }

    // 检查是否已选择区服和角色
    if (!selectedServerId || !selectedRoleId) {
      if (serverRoleList.length > 0) {
        alert(t('errors.pleaseSelectServerRole'));
      } else {
        alert(t('errors.serverRoleInfoFailed'));
      }
      return;
    }

    // 打开支付弹窗
    setIsPaymentModalOpen(true);
  };

  const handleLoginClick = () => {
    setIsLoginModalOpen(true);
  };
  
  const handleLogout = () => {
    // 默认仅从当前游戏登出
    localStorage.removeItem(`gameUser_${gameId}`);
    localStorage.removeItem('serverRoleList');

    // 如果当前的 gameUser 是当前游戏的，也清理它
    const currentGameUser = localStorage.getItem('gameUser');
    if (currentGameUser) {
      try {
        const userData = JSON.parse(currentGameUser);
        if (userData.gameId === gameId) {
          localStorage.removeItem('gameUser');
        }
      } catch (e) {
        // 如果解析失败，为安全起见也清理
        localStorage.removeItem('gameUser');
      }
    }

    setUserInfo(null);
    setSelectedServerId('');
    setSelectedRoleId('');
    setServerRoleList([]);
  };


  
  // 加载状态
  if (loading) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center py-20">
          <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-zinc-400">加载中...</p>
        </div>
      </Layout>
    );
  }
  
  // 错误状态
  if (error || !gameWithCurrency) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center py-20">
          <h1 className="text-2xl font-bold text-white mb-4">{t('ui.gameNotFound')}</h1>
          <p className="text-zinc-400 mb-8">{error || t('ui.gameNotFoundDesc')}</p>
          <Link
            href={`/${locale}`}
            className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-lg transition-colors"
          >
            {t('ui.backToHome')}
          </Link>
        </div>
      </Layout>
    );
  }
  
  // 确保图片路径存在，如果不存在则使用默认图片
  const bannerUrl = gameWithCurrency.bannerUrl || "/images/games/1.png";
  
  return (
    <Layout>
      <div className="mb-8">
        <Link
          href={`/${locale}`}
          className="inline-flex items-center text-sm font-medium text-indigo-400 hover:text-indigo-300 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15 19l-7-7 7-7"
            />
          </svg>
          {t('ui.backToHome')}
        </Link>
      </div>

      <div className="relative h-80 sm:h-96 md:h-120 lg:h-140 rounded-2xl overflow-hidden mb-8">
        <Image
          src={bannerUrl}
          alt={gameWithCurrency.title || gameWithCurrency.id}
          fill
          className="object-cover object-center"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent" />
        <div className="absolute bottom-0 left-0 right-0 p-6 md:p-8">
          <div className="flex justify-between items-end">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">
                {gameWithCurrency.title || gameWithCurrency.id}
              </h1>
              <div className="flex items-center space-x-2 sm:space-x-4">
                <span className="px-2 py-1 text-xs sm:text-sm font-semibold rounded-full bg-indigo-500/20 text-indigo-300">
                  {gameWithCurrency.category || "游戏"}
                </span>
                <span className="px-2 py-1 text-xs sm:text-sm text-zinc-300 flex items-center bg-zinc-800/50 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                    />
                  </svg>
                  <span>{gameWithCurrency.rating}</span>
                </span>
                <span className="px-2 py-1 text-xs sm:text-sm text-zinc-300 flex items-center bg-zinc-800/50 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth={2}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                  <span>{gameWithCurrency.playerCount}</span>
                </span>
              </div>
            </div>
            {userInfo ? (
              <div className="flex flex-col sm:flex-row items-end sm:items-center gap-2 sm:gap-4">
                <div className="text-right">
                  <p className="text-xs sm:text-sm text-zinc-400">{t('login.welcome')}</p>
                  <p className="text-sm sm:text-base text-white font-medium truncate max-w-[120px] sm:max-w-[160px]">{userInfo.username}</p>
                </div>
                <button
                  onClick={handleLogout}
                  className="px-3 sm:px-4 py-1.5 sm:py-2 bg-zinc-800 hover:bg-zinc-700 text-white text-xs sm:text-sm rounded-lg transition-colors whitespace-nowrap flex items-center gap-1"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3 w-3"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth={2}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                    />
                  </svg>
                  {t('login.logout')}
                </button>
              </div>
            ) : (
              <div className="flex flex-col items-end gap-2">
                <button
                  onClick={handleLoginClick}
                  className="px-4 sm:px-6 py-2 sm:py-3 bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-medium rounded-lg transition-colors flex items-center text-sm sm:text-base"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth={2}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                    />
                  </svg>
                  {t('ui.login')}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>



      <div className="mb-12">
        <h2 className="text-xl font-bold text-white mb-2">{t('ui.gameIntro')}</h2>
        <p className="text-zinc-300">{gameWithCurrency.description || "暂无描述"}</p>
      </div>

      {/* 区服角色选择 */}
      {userInfo && serverRoleList.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold text-white mb-4">{t('serverRole.title')}</h2>
          <div className="bg-zinc-900/50 rounded-lg p-6 border border-zinc-800">
            <ServerRoleSelector
              serverRoleList={serverRoleList}
              onSelect={handleServerRoleSelect}
              selectedServerId={selectedServerId}
              selectedRoleId={selectedRoleId}
            />
          </div>
        </div>
      )}

      <div className="mb-8">
        <h2 className="text-xl font-bold text-white mb-6">{t('ui.selectRechargeItem')}</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {gameWithCurrency.products.map((product) => (
            <ProductCard
              key={product.id}
              id={product.id}
              title={product.title || `${product.id}`}
              imageUrl={product.imageUrl || "/images/games/1.png"}
              price={product.price}
              originalPrice={product.originalPrice}
              currency={product.currency || gameWithCurrency.currency}
              description={product.description || ""}
              onSelect={handleProductSelect}
              selected={selectedProductId === product.id}
            />
          ))}
        </div>
      </div>

      <div className="sticky bottom-0 left-0 right-0 p-4 bg-zinc-900/80 backdrop-blur-lg border-t border-zinc-800/50 z-10 touch-none">
        <div className="container mx-auto flex items-center justify-between pointer-events-auto">
          <div className="select-none">
            {selectedProduct ? (
              <>
                <p className="text-sm text-zinc-400 mb-1">{t('ui.selected')}</p>
                <p className="text-lg font-bold text-white">
                  {selectedProduct.title || selectedProduct.id} - {getCurrencySymbol(selectedProduct.currency || gameWithCurrency.currency)}{selectedProduct.price}
                </p>
                {userInfo && (!selectedServerId || !selectedRoleId) && (
                  <p className="text-sm text-orange-400 mt-1">
                    ⚠️ {t('serverRole.pleaseSelectFirst')}
                  </p>
                )}
              </>
            ) : (
              <p className="text-zinc-400">{t('ui.pleaseSelect')}</p>
            )}
          </div>
          <button
            onClick={handlePaymentClick}
            disabled={!selectedProductId || Boolean(userInfo && (!selectedServerId || !selectedRoleId))}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              selectedProductId && (!userInfo || (selectedServerId && selectedRoleId))
                ? "bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white"
                : "bg-zinc-800 text-zinc-500 cursor-not-allowed"
            }`}
            style={{ touchAction: 'manipulation' }}
          >
            {!selectedProductId
              ? t('ui.payNow')
              : userInfo && (!selectedServerId || !selectedRoleId)
              ? t('serverRole.pleaseSelectFirst')
              : t('ui.payNow')
            }
          </button>
        </div>
      </div>

      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        game={gameWithCurrency}
        productId={productId || undefined}
        serverUrl={serverUrl}
      />

      <PaymentSelectionModal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        product={selectedProduct || null}
        game={gameWithCurrency}
        userInfo={userInfo}
        locale={locale}
        selectedServerId={selectedServerId}
        selectedRoleId={selectedRoleId}
        siteId={siteId || 1} // 传递 siteId，如果为空则使用默认值 1
      />

      <PaymentStatusModal
        isOpen={paymentStatusModal.isOpen}
        onClose={handlePaymentStatusModalClose}
        status={paymentStatusModal.status}
        message={paymentStatusModal.message}
        orderNo={paymentStatusModal.orderNo}
      />
    </Layout>
  );
}